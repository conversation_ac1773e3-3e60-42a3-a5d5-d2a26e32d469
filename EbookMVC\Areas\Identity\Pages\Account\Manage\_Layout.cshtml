@{
    if (ViewData.TryGetValue("ParentLayout", out var parentLayout))
    {
        Layout = (string)parentLayout;
    }
    else
    {
        Layout = "/Views/Shared/_Layout.cshtml";
    }
}

<div class="row">
    <div class="col-md-3">
        <partial name="_ManageNav" />
    </div>
    <div class="col-md-9">
        @RenderBody()
    </div>
</div>

@section Scripts {
    @RenderSection("Scripts", required: false)
}