@page
@model EmailModel
@{
    ViewData["Title"] = "Quản lý Email";
    ViewData["ActivePage"] = ManageNavPages.Email;
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@ViewData["Title"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-page="./Index">Quản lý tài khoản</a></li>
                    <li class="breadcrumb-item active">Email</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3">
                <partial name="_ManageNav" />
            </div>
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-envelope"></i>
                            Quản lý Email
                        </h3>
                    </div>
                    <div class="card-body">
                        <partial name="_StatusMessage" for="StatusMessage" />
                        <form id="email-form" method="post">
                            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>
                            
                            @if (Model.IsEmailConfirmed)
                            {
                                <div class="form-group">
                                    <label class="form-label">Email hiện tại</label>
                                    <div class="input-group">
                                        <input class="form-control" value="@Model.Email" disabled />
                                        <div class="input-group-text text-success">
                                            <i class="fas fa-check-circle"></i>
                                            Đã xác thực
                                        </div>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="form-group">
                                    <label class="form-label">Email hiện tại</label>
                                    <div class="input-group">
                                        <input class="form-control" value="@Model.Email" disabled />
                                        <div class="input-group-text text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Chưa xác thực
                                        </div>
                                    </div>
                                    <button id="email-verification" type="submit" asp-page-handler="SendVerificationEmail" class="btn btn-link">
                                        Gửi email xác thực
                                    </button>
                                </div>
                            }
                            
                            <div class="form-group">
                                <label asp-for="Input.NewEmail" class="form-label">Email mới</label>
                                <input asp-for="Input.NewEmail" class="form-control" autocomplete="email" aria-required="true" placeholder="Vui lòng nhập email mới." />
                                <span asp-validation-for="Input.NewEmail" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <button id="change-email-button" type="submit" asp-page-handler="ChangeEmail" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Thay đổi email
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
