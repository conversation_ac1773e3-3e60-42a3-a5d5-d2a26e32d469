@page
@model ChangePasswordModel
@{
    ViewData["Title"] = "Đổi mật khẩu";
    ViewData["ActivePage"] = ManageNavPages.ChangePassword;
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@ViewData["Title"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                    <li class="breadcrumb-item"><a asp-page="./Index">Quản lý tài khoản</a></li>
                    <li class="breadcrumb-item active">Đổi mật khẩu</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3">
                <partial name="_ManageNav" />
            </div>
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-key"></i>
                            Đổi mật khẩu
                        </h3>
                    </div>
                    <div class="card-body">
                        <partial name="_StatusMessage" for="StatusMessage" />
                        <form id="change-password-form" method="post">
                            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>
                            
                            <div class="form-group">
                                <label asp-for="Input.OldPassword" class="form-label">Mật khẩu hiện tại</label>
                                <input asp-for="Input.OldPassword" class="form-control" autocomplete="current-password" aria-required="true" placeholder="Vui lòng nhập mật khẩu hiện tại." />
                                <span asp-validation-for="Input.OldPassword" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="Input.NewPassword" class="form-label">Mật khẩu mới</label>
                                <input asp-for="Input.NewPassword" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Vui lòng nhập mật khẩu mới." />
                                <span asp-validation-for="Input.NewPassword" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <label asp-for="Input.ConfirmPassword" class="form-label">Xác nhận mật khẩu mới</label>
                                <input asp-for="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Vui lòng xác nhận mật khẩu mới." />
                                <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Cập nhật mật khẩu
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
