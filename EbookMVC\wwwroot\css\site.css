/* Custom styles for Elearn */

html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

/* Fix for AdminLTE */
.content-wrapper {
  min-height: calc(100vh - 114px) !important;
}

/* Custom card styles */
.card {
  box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
  margin-bottom: 1rem;
}

.card-header {
  border-bottom: 1px solid rgba(0,0,0,.125);
  padding: .75rem 1.25rem;
}

.card-title {
  float: left;
  font-size: 1.1rem;
  font-weight: 400;
  margin: 0;
}

/* Custom table styles */
.table-responsive {
  overflow-x: auto;
}

/* Custom form styles */
.form-group {
  margin-bottom: 1rem;
}

/* Custom button styles */
.btn-group-sm>.btn, .btn-sm {
  padding: .25rem .5rem;
  font-size: .875rem;
  line-height: 1.5;
  border-radius: .2rem;
}

/* Custom badge styles */
.badge {
  display: inline-block;
  padding: .25em .4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25rem;
}

/* Custom sidebar styles */
.sidebar-dark-primary .nav-sidebar>.nav-item>.nav-link.active {
  background-color: #007bff;
  color: #fff;
}

.sidebar-mini .main-sidebar .nav-sidebar .nav-link {
  padding: 12px;
  transition: all 0.3s ease;
}

.sidebar-mini .main-sidebar .nav-sidebar .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-mini .main-sidebar .nav-sidebar .nav-link p {
  margin-bottom: 0;
}

.sidebar-mini .main-sidebar .nav-sidebar .nav-link i {
  margin-right: 10px;
}

.sidebar-mini.sidebar-collapse .main-sidebar .nav-sidebar .nav-link i {
  margin-right: 0;
}

.sidebar-mini .main-sidebar .brand-link {
  padding: 15px;
  display: flex;
  align-items: center;
}

.sidebar-mini .main-sidebar .brand-link .brand-image {
  margin-right: 10px;
}

.sidebar-mini .main-sidebar .brand-link .brand-text {
  font-weight: 300;
}

.sidebar-mini.sidebar-collapse .main-sidebar .brand-link .brand-text {
  opacity: 0;
  width: 0;
}

.sidebar-mini .main-sidebar .user-panel {
  padding: 15px;
  display: flex;
  align-items: center;
}

.sidebar-mini .main-sidebar .user-panel .image {
  margin-right: 10px;
}

.sidebar-mini .main-sidebar .user-panel .info {
  overflow: hidden;
  white-space: nowrap;
}

.sidebar-mini.sidebar-collapse .main-sidebar .user-panel .info {
  opacity: 0;
  width: 0;
}

/* Custom navbar styles */
.navbar-light .navbar-nav .nav-link {
  color: rgba(0,0,0,.5);
}

.navbar-light .navbar-nav .nav-link:hover {
  color: rgba(0,0,0,.7);
}

/* Custom footer styles */
.main-footer {
  background-color: #fff;
  border-top: 1px solid #dee2e6;
  color: #869099;
  padding: 1rem;
}

/* Custom breadcrumb styles */
.breadcrumb {
  background-color: transparent;
  margin-bottom: 0;
  padding: 0;
  list-style: none;
}

/* Custom info-box styles */
.info-box {
  box-shadow: 0 0 1px rgba(0,0,0,.125), 0 1px 3px rgba(0,0,0,.2);
  border-radius: .25rem;
  background-color: #fff;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 1rem;
  min-height: 80px;
  padding: .5rem;
  position: relative;
  width: 100%;
}

.info-box .info-box-icon {
  border-radius: .25rem;
  -ms-flex-align: center;
  align-items: center;
  display: -ms-flexbox;
  display: flex;
  font-size: 1.875rem;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  width: 70px;
}

.info-box .info-box-content {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  line-height: 1.8;
  -ms-flex: 1;
  flex: 1;
  padding: 0 10px;
}

.info-box .info-box-text, .info-box .progress-description {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-box .info-box-number {
  display: block;
  font-weight: 700;
}

/* Custom dropdown styles */
.dropdown-menu-lg {
  max-width: 300px;
  min-width: 280px;
  padding: 0;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: .25rem 1rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

.dropdown-item:hover, .dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa;
}

/* Custom product list styles */
.products-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.products-list>.item {
  border-radius: .25rem;
  background-color: #fff;
  padding: 10px 0;
}

.products-list>.item:after {
  display: block;
  clear: both;
  content: "";
}

.product-list-in-card>.item {
  border-radius: 0;
  border-bottom: 1px solid rgba(0,0,0,.125);
}

.product-list-in-card>.item:last-of-type {
  border-bottom-width: 0;
}

.product-img {
  float: left;
  text-align: center;
  width: 50px;
}

.product-img img {
  width: 100% !important;
}

.product-info {
  margin-left: 60px;
}

.product-title {
  font-weight: 600;
}

.product-description {
  display: block;
  color: #6c757d;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
